/**
 * Enhanced Asset Edit Form JavaScript
 * Provides improved UX with animations, validation, and user feedback
 */

document.addEventListener('DOMContentLoaded', function() {
    try {
        initEnhancedForm();
        initLoadingStates();
        initFormValidation();
        initAutoSave();
        initAccessibilityFeatures();
        initMobileOptimizations();

        console.log('Enhanced asset edit form initialized successfully');
    } catch (error) {
        console.error('Error initializing enhanced form:', error);
        // Fallback to basic functionality
        initBasicFallback();
    }
});

/**
 * Initialize enhanced form features
 */
function initEnhancedForm() {
    // Add smooth scroll to error fields
    const errorFields = document.querySelectorAll('.is-invalid');
    if (errorFields.length > 0) {
        setTimeout(() => {
            errorFields[0].scrollIntoView({
                behavior: 'smooth',
                block: 'center'
            });
            errorFields[0].focus();
        }, 500);
    }

    // Initialize character counters
    initCharacterCounters();

    // Enhanced card focus effects
    const cards = document.querySelectorAll('.react-card');
    cards.forEach(card => {
        const inputs = card.querySelectorAll('input, select, textarea');

        inputs.forEach(input => {
            input.addEventListener('focus', () => {
                card.classList.add('card-focused');
                // Smooth scroll to ensure visibility
                setTimeout(() => {
                    input.scrollIntoView({
                        behavior: 'smooth',
                        block: 'nearest'
                    });
                }, 100);
            });

            input.addEventListener('blur', () => {
                // Check if any input in this card still has focus
                setTimeout(() => {
                    if (!card.contains(document.activeElement)) {
                        card.classList.remove('card-focused');
                    }
                }, 100);
            });
        });
    });

    // Add progress indicator
    addProgressIndicator();
}

/**
 * Initialize loading states for form submission
 */
function initLoadingStates() {
    const form = document.querySelector('form');
    const submitBtn = document.querySelector('button[type="submit"]');

    if (form && submitBtn) {
        form.addEventListener('submit', function(e) {
            // Show loading overlay
            showLoadingOverlay();

            // Disable submit button and show loading state
            submitBtn.disabled = true;
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Updating...';

            // Re-enable after timeout (fallback)
            setTimeout(() => {
                hideLoadingOverlay();
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
            }, 10000);
        });
    }
}

/**
 * Enhanced form validation
 */
function initFormValidation() {
    const form = document.querySelector('form');
    const inputs = form.querySelectorAll('input, select, textarea');

    inputs.forEach(input => {
        // Real-time validation
        input.addEventListener('input', function() {
            validateField(this);
        });

        input.addEventListener('blur', function() {
            validateField(this);
        });

        // Enhanced focus styling
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('field-focused');
        });

        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('field-focused');
        });
    });

    // Form submission validation
    form.addEventListener('submit', function(e) {
        let isValid = true;
        const requiredFields = form.querySelectorAll('[required]');

        requiredFields.forEach(field => {
            if (!validateField(field)) {
                isValid = false;
            }
        });

        if (!isValid) {
            e.preventDefault();
            showValidationError();
        }
    });
}

/**
 * Validate individual field
 */
function validateField(field) {
    const value = field.value.trim();
    const isRequired = field.hasAttribute('required');
    let isValid = true;

    // Remove existing validation classes
    field.classList.remove('is-valid', 'is-invalid');

    // Check if required field is empty
    if (isRequired && !value) {
        field.classList.add('is-invalid');
        isValid = false;
    } else if (value) {
        // Field has value, validate based on type
        if (field.type === 'email' && !isValidEmail(value)) {
            field.classList.add('is-invalid');
            isValid = false;
        } else if (field.type === 'number' && isNaN(value)) {
            field.classList.add('is-invalid');
            isValid = false;
        } else {
            field.classList.add('is-valid');
        }
    }

    return isValid;
}

/**
 * Auto-save functionality (optional)
 */
function initAutoSave() {
    const form = document.querySelector('form');
    const inputs = form.querySelectorAll('input, select, textarea');
    let autoSaveTimeout;

    // Load saved data
    loadAutoSavedData();

    inputs.forEach(input => {
        input.addEventListener('input', function() {
            clearTimeout(autoSaveTimeout);
            autoSaveTimeout = setTimeout(() => {
                saveFormData();
            }, 2000); // Save after 2 seconds of inactivity
        });
    });

    // Clear auto-save on successful submission
    form.addEventListener('submit', function() {
        clearAutoSavedData();
    });
}

/**
 * Initialize accessibility features
 */
function initAccessibilityFeatures() {
    // Add ARIA labels
    const inputs = document.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
        const label = input.parentElement.querySelector('label');
        if (label && !input.getAttribute('aria-label')) {
            input.setAttribute('aria-label', label.textContent);
        }
    });

    // Keyboard navigation improvements
    document.addEventListener('keydown', function(e) {
        // Escape key to cancel
        if (e.key === 'Escape') {
            const cancelBtn = document.querySelector('.react-btn-secondary');
            if (cancelBtn) {
                cancelBtn.click();
            }
        }

        // Ctrl+S to save
        if (e.ctrlKey && e.key === 's') {
            e.preventDefault();
            const submitBtn = document.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.click();
            }
        }
    });
}

/**
 * Mobile optimizations
 */
function initMobileOptimizations() {
    if (window.innerWidth <= 768) {
        // Adjust input types for mobile
        const numberInputs = document.querySelectorAll('input[type="number"]');
        numberInputs.forEach(input => {
            input.setAttribute('inputmode', 'numeric');
        });

        // Prevent zoom on input focus
        const viewport = document.querySelector('meta[name="viewport"]');
        if (viewport) {
            const inputs = document.querySelectorAll('input, select, textarea');
            inputs.forEach(input => {
                input.addEventListener('focus', () => {
                    viewport.setAttribute('content', 'width=device-width, initial-scale=1, maximum-scale=1');
                });

                input.addEventListener('blur', () => {
                    viewport.setAttribute('content', 'width=device-width, initial-scale=1');
                });
            });
        }
    }
}

/**
 * Utility functions
 */
function showLoadingOverlay() {
    let overlay = document.querySelector('.loading-overlay');
    if (!overlay) {
        overlay = document.createElement('div');
        overlay.className = 'loading-overlay';
        overlay.innerHTML = '<div class="loading-spinner"></div>';
        document.body.appendChild(overlay);
    }
    overlay.style.display = 'flex';
}

function hideLoadingOverlay() {
    const overlay = document.querySelector('.loading-overlay');
    if (overlay) {
        overlay.style.display = 'none';
    }
}

function showValidationError() {
    const firstInvalidField = document.querySelector('.is-invalid');
    if (firstInvalidField) {
        firstInvalidField.scrollIntoView({ behavior: 'smooth', block: 'center' });
        firstInvalidField.focus();

        // Show toast notification
        showToast('Please fill in all required fields', 'error');
    }
}

function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `toast-notification toast-${type}`;
    toast.textContent = message;

    // Style the toast
    Object.assign(toast.style, {
        position: 'fixed',
        top: '20px',
        right: '20px',
        padding: '1rem 1.5rem',
        borderRadius: '8px',
        color: 'white',
        fontWeight: '500',
        zIndex: '10000',
        transform: 'translateX(100%)',
        transition: 'transform 0.3s ease'
    });

    if (type === 'error') {
        toast.style.background = '#dc3545';
    } else if (type === 'success') {
        toast.style.background = '#28a745';
    } else {
        toast.style.background = '#17a2b8';
    }

    document.body.appendChild(toast);

    // Animate in
    setTimeout(() => {
        toast.style.transform = 'translateX(0)';
    }, 100);

    // Remove after delay
    setTimeout(() => {
        toast.style.transform = 'translateX(100%)';
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 300);
    }, 3000);
}

function addProgressIndicator() {
    const form = document.querySelector('form');
    const requiredFields = form.querySelectorAll('[required]');

    if (requiredFields.length > 0) {
        const progressContainer = document.createElement('div');
        progressContainer.className = 'form-progress';
        progressContainer.innerHTML = `
            <div class="progress-bar-container">
                <div class="progress-bar-fill" style="width: 0%"></div>
            </div>
            <span class="progress-text">0% Complete</span>
        `;

        // Style the progress indicator
        Object.assign(progressContainer.style, {
            position: 'fixed',
            top: '70px',
            right: '20px',
            background: 'white',
            padding: '0.75rem',
            borderRadius: '8px',
            boxShadow: '0 4px 15px rgba(0,0,0,0.1)',
            fontSize: '0.8rem',
            zIndex: '1000'
        });

        document.body.appendChild(progressContainer);

        // Update progress on input
        requiredFields.forEach(field => {
            field.addEventListener('input', updateProgress);
        });

        updateProgress();
    }
}

function updateProgress() {
    const form = document.querySelector('form');
    const requiredFields = form.querySelectorAll('[required]');
    const filledFields = Array.from(requiredFields).filter(field => field.value.trim() !== '');

    const percentage = Math.round((filledFields.length / requiredFields.length) * 100);

    const progressFill = document.querySelector('.progress-bar-fill');
    const progressText = document.querySelector('.progress-text');

    if (progressFill && progressText) {
        progressFill.style.width = percentage + '%';
        progressFill.style.background = percentage === 100 ? '#28a745' : '#2E7D32';
        progressText.textContent = percentage + '% Complete';
    }
}

function saveFormData() {
    const form = document.querySelector('form');
    const formData = new FormData(form);
    const data = {};

    for (let [key, value] of formData.entries()) {
        data[key] = value;
    }

    localStorage.setItem('asset_edit_autosave', JSON.stringify(data));
}

function loadAutoSavedData() {
    const savedData = localStorage.getItem('asset_edit_autosave');
    if (savedData) {
        try {
            const data = JSON.parse(savedData);
            Object.keys(data).forEach(key => {
                const field = document.querySelector(`[name="${key}"]`);
                if (field && !field.value) {
                    field.value = data[key];
                }
            });
        } catch (e) {
            console.log('Error loading auto-saved data:', e);
        }
    }
}

function clearAutoSavedData() {
    localStorage.removeItem('asset_edit_autosave');
}

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function initCharacterCounters() {
    const fieldsWithCounters = document.querySelectorAll('input[maxlength], textarea[maxlength]');

    fieldsWithCounters.forEach(field => {
        const maxLength = field.getAttribute('maxlength');
        if (maxLength) {
            // Create counter element
            const counter = document.createElement('small');
            counter.className = 'character-counter text-muted';
            counter.style.cssText = 'position: absolute; right: 0.75rem; bottom: 0.25rem; font-size: 0.7rem;';

            // Update counter function
            const updateCounter = () => {
                const remaining = maxLength - field.value.length;
                counter.textContent = `${field.value.length}/${maxLength}`;

                if (remaining < 20) {
                    counter.style.color = '#dc3545';
                } else if (remaining < 50) {
                    counter.style.color = '#ffc107';
                } else {
                    counter.style.color = '#6c757d';
                }
            };

            // Add counter to field container
            field.parentElement.style.position = 'relative';
            field.parentElement.appendChild(counter);

            // Initialize and bind events
            updateCounter();
            field.addEventListener('input', updateCounter);
        }
    });
}

function initBasicFallback() {
    console.log('Initializing basic fallback functionality');

    // Basic form validation
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            const requiredFields = form.querySelectorAll('[required]');
            let isValid = true;

            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    field.style.borderColor = '#dc3545';
                    isValid = false;
                } else {
                    field.style.borderColor = '';
                }
            });

            if (!isValid) {
                e.preventDefault();
                alert('Please fill in all required fields.');
            }
        });
    }

    // Basic loading state
    const submitBtn = document.querySelector('button[type="submit"]');
    if (submitBtn) {
        submitBtn.addEventListener('click', function() {
            this.disabled = true;
            this.textContent = 'Updating...';

            setTimeout(() => {
                this.disabled = false;
                this.innerHTML = '<i class="fas fa-save react-btn-icon"></i>Update Asset';
            }, 5000);
        });
    }
}
